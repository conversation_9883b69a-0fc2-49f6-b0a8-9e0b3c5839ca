
<?php
    $profile = \App\Models\Utility::get_file('uploads/avatar');
?>
<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Sub-Accounts')); ?>

    <span class="badge bg-primary align-middle ms-2" style="font-size:1rem;"><?php echo e($totalCompaniesCount); ?></span>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a>
    </li>
    <li class="breadcrumb-item"><?php echo e(__('Sub-Accounts (Companies)')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <?php if(\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('create sub accounts'))): ?>
            <a href="<?php echo e(route('system-admin.companies.create')); ?>" class="btn btn-sm btn-primary me-1"
                data-bs-toggle="tooltip" data-title="<?php echo e(__('Create Sub-Account')); ?>" data-bs-original-title="<?php echo e(__('Create Sub-Account')); ?>">
                <i class="ti ti-plus"></i>
            </a>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>

<div class="d-flex justify-content-between align-items-center mb-3">
    <div class="per-page-selector">
        <form method="GET" action="<?php echo e(route('system-admin.companies')); ?>" class="d-flex align-items-center gap-2">
            <label for="per_page" class="form-label mb-0 text-muted small"><?php echo e(__('Show')); ?>:</label>
            <select name="per_page" id="per_page" class="form-select form-select-sm" style="width: auto;" onchange="this.form.submit()">
                <option value="10" <?php echo e(request('per_page', 10) == 10 ? 'selected' : ''); ?>>10</option>
                <option value="25" <?php echo e(request('per_page', 10) == 25 ? 'selected' : ''); ?>>25</option>
                <option value="50" <?php echo e(request('per_page', 10) == 50 ? 'selected' : ''); ?>>50</option>
                <option value="100" <?php echo e(request('per_page', 10) == 100 ? 'selected' : ''); ?>>100</option>
            </select>
            <span class="text-muted small"><?php echo e(__('entries')); ?></span>
        </form>
    </div>
    <div class="total-count">
        <span class="text-muted small">
            <?php echo e(__('Total')); ?>: <strong><?php echo e($totalCompaniesCount); ?></strong> <?php echo e(__('companies')); ?>

        </span>
    </div>
</div>

<div class="table-responsive">
    <table class="table table-striped align-middle mb-0">
        <thead class="table-light">
            <tr>
                <th><?php echo e(__('Company Name')); ?></th>
                <th><?php echo e(__('Email')); ?></th>
                <th><?php echo e(__('Plan')); ?></th>
                <th><?php echo e(__('Last Login')); ?></th>
                <th><?php echo e(__('Users')); ?></th>
                <th><?php echo e(__('Customers')); ?></th>
                <th><?php echo e(__('Vendors')); ?></th>
                <th><?php echo e(__('Status')); ?></th>
                <th><?php echo e(__('Actions')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $companies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $company): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td class="fw-semibold">
                        <div class="d-flex align-items-center gap-2">
                            <img src="<?php echo e(!empty($company->avatar) ? Utility::get_file('uploads/avatar/') . $company->avatar : asset(Storage::url('uploads/avatar/avatar.png'))); ?>" alt="user-image" class="rounded-circle border border-primary" style="width:36px;height:36px;object-fit:cover;">
                            <span><?php echo e($company->name); ?></span>
                        </div>
                    </td>
                    <td class="text-break"><?php echo e($company->email); ?></td>
                    <td>
                        <span class="badge bg-primary">
                            <?php echo e(!empty($company->plan) && is_object($company->plan) ? $company->plan->name : (!empty($company->plan) ? 'Plan ID: ' . $company->plan : __('No Plan'))); ?>

                        </span>
                    </td>
                    <td>
                        <?php if($company->last_login_at): ?>
                            <?php echo e(\Carbon\Carbon::parse($company->last_login_at)->format('M d, Y \a\t H:i')); ?>

                        <?php else: ?>
                            <span class="text-muted"><?php echo e(__('Never logged in')); ?></span>
                        <?php endif; ?>
                    </td>
                    <td><?php echo e($company->totalCompanyUser($company->id)); ?></td>
                    <td><?php echo e($company->totalCompanyCustomer($company->id)); ?></td>
                    <td><?php echo e($company->totalCompanyVender($company->id)); ?></td>
                    <td>
                        <?php if($company->delete_status == 0): ?>
                            <span class="badge bg-danger"><?php echo e(__('Soft Deleted')); ?></span>
                        <?php else: ?>
                            <span class="badge bg-success"><?php echo e(__('Active')); ?></span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <div class="btn-group">
                            <?php if(\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('manage sub accounts'))): ?>
                            <a href="<?php echo e(route('login.with.company', $company->id)); ?>"
                                data-bs-toggle="tooltip"
                                title="<?php echo e(__('Login')); ?>"
                                style="display:inline-flex;align-items:center;gap:8px;padding:0 16px;height:40px;border-radius:999px;background:linear-gradient(to right, #065f46, #0f766e);color:white;font-weight:500;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                                onmouseover="this.style.transform='scale(1.05)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-replace" style="font-size:16px;"></i>
                                <span><?php echo e(__('Login')); ?></span>
                            </a>
                            <?php endif; ?>
                            <?php if(\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('edit sub accounts'))): ?>
                            <a href="<?php echo e(route('system-admin.companies.edit', $company->id)); ?>"
                                data-bs-toggle="tooltip"
                                title="<?php echo e(__('Edit')); ?>"
                                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;
                                        background:linear-gradient(to right, #065f46, #0f766e);color:white;
                                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                onmouseover="this.style.transform='scale(1.1)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-pencil"></i>
                            </a>
                            <?php endif; ?>
                            <?php if(\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('delete sub accounts'))): ?>
                                <?php echo Form::open(['method' => 'DELETE', 'route' => ['system-admin.companies.destroy', $company['id']], 'id' => 'delete-form-' . $company['id'], 'style' => 'display:inline-block']); ?>

                                <button type="button"
                                    data-bs-toggle="tooltip"
                                    title="<?php echo e(__('Delete Company')); ?>"
                                    class="bs-pass-para-delete"
                                    data-confirm-text="<?php echo e(__('Are you sure you want to delete this company?')); ?>"
                                    style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                                            border-radius:50%;background:white;color:#dc2626;border:1.5px solid #dc2626;
                                            box-shadow:0 4px 6px rgba(0,0,0,0.05);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                    onmouseover="this.style.transform='scale(1.1)'"
                                    onmouseout="this.style.transform='scale(1)'">
                                    <i class="ti ti-trash"></i>
                                </button>
                                <?php echo Form::close(); ?>

                                <?php if($company->delete_status != 0): ?>
                                    <?php echo Form::open(['method' => 'DELETE', 'route' => ['system-admin.companies.force-destroy', $company['id']], 'id' => 'force-delete-form-' . $company['id'], 'style' => 'display:inline-block']); ?>

                                    <button type="button"
                                        class="bs-pass-para-force"
                                        data-confirm-text="This will force delete the company even if modules have dependency errors. Are you sure?"
                                        title="<?php echo e(__('Force Delete')); ?>"
                                        data-bs-toggle="tooltip"
                                        style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                                                border-radius:50%;background:white;color:#dc2626;border:1.5px solid #dc2626;
                                                box-shadow:0 4px 6px rgba(0,0,0,0.05);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                        onmouseover="this.style.transform='scale(1.1)'"
                                        onmouseout="this.style.transform='scale(1)'">
                                        <i class="ti ti-alert-triangle"></i>
                                    </button>
                                    <?php echo Form::close(); ?>

                                <?php endif; ?>
                            <?php endif; ?>
                            <?php if($company->is_enable_login == 1): ?>
                            <a href="<?php echo e(route('system-admin.companies.login', \Crypt::encrypt($company->id))); ?>"
                                title="<?php echo e(__('Login Disable')); ?>"
                                data-bs-toggle="tooltip"
                                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                                        border-radius:50%;background:white;color:#eab308;border:1.5px solid #eab308;
                                        box-shadow:0 4px 6px rgba(0,0,0,0.05);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                onmouseover="this.style.transform='scale(1.1)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-road-sign"></i>
                            </a>
                            <?php elseif($company->is_enable_login == 0 && $company->password == null): ?>
                            <a href="#"
                                data-url="<?php echo e(route('system-admin.companies.reset', \Crypt::encrypt($company->id))); ?>"
                                data-ajax-popup="true"
                                data-size="md"
                                data-bs-toggle="tooltip"
                                title="<?php echo e(__('New Password')); ?>"
                                class="login_enable"
                                style="display:inline-flex;align-items:center;gap:8px;padding:0 16px;height:40px;border-radius:999px;
                                        background:linear-gradient(to right, #065f46, #0f766e);color:white;font-weight:500;
                                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                                onmouseover="this.style.transform='scale(1.05)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-road-sign" style="font-size:16px;"></i>
                            </a>
                            <?php else: ?>
                            <a href="<?php echo e(route('system-admin.companies.login', \Crypt::encrypt($company->id))); ?>"
                                data-bs-toggle="tooltip"
                                title="<?php echo e(__('Login Enable')); ?>"
                                style="display:inline-flex;align-items:center;gap:8px;padding:0 16px;height:40px;border-radius:999px;
                                        background:linear-gradient(to right, #065f46, #0f766e);color:white;font-weight:500;
                                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                onmouseover="this.style.transform='scale(1.05)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-road-sign" style="font-size:16px;"></i>
                            </a>
                            <?php endif; ?>
                        </div>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
</div>


<?php if($companies->hasPages()): ?>
<div class="d-flex justify-content-between align-items-center mt-4 flex-wrap gap-3">
    <div class="pagination-info">
        <span class="text-muted small">
            <?php echo e(__('Showing')); ?>

            <strong><?php echo e($companies->firstItem()); ?></strong>
            <?php echo e(__('to')); ?>

            <strong><?php echo e($companies->lastItem()); ?></strong>
            <?php echo e(__('of')); ?>

            <strong><?php echo e($companies->total()); ?></strong>
            <?php echo e(__('results')); ?>

        </span>
    </div>
    <div class="pagination-wrapper">
        <?php echo e($companies->appends(request()->query())->links('custom.pagination')); ?>

    </div>
</div>
<?php endif; ?>

<!-- Force Delete Confirmation Modal -->
<div class="modal fade" id="forceDeleteModal" tabindex="-1" aria-labelledby="forceDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="forceDeleteModalLabel">
                    <i class="ti ti-alert-triangle text-danger me-2"></i>
                    <?php echo e(__('Force Delete Confirmation')); ?>

                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger d-flex align-items-center mb-3">
                    <i class="ti ti-alert-circle me-2"></i>
                    <div>
                        <strong><?php echo e(__('Warning!')); ?></strong> <?php echo e(__('This action cannot be undone.')); ?>

                    </div>
                </div>
                
                <p id="forceDeleteConfirmText" class="mb-3">
                    <?php echo e(__('This will force delete the company even if modules have dependency errors. Are you sure?')); ?>

                </p>
                
                <div class="mb-3">
                    <label for="forceDeleteInput" class="form-label">
                        <?php echo e(__('Type')); ?> <strong class="text-danger">DELETE</strong> <?php echo e(__('to confirm:')); ?>

                    </label>
                    <input type="text" 
                           class="form-control" 
                           id="forceDeleteInput" 
                           placeholder="<?php echo e(__('Type DELETE to confirm')); ?>"
                           autocomplete="off">
                    <div id="forceDeleteError" class="invalid-feedback" style="display: none;">
                        <?php echo e(__('Please type DELETE exactly as shown above.')); ?>

                    </div>
                </div>
                
                <div class="text-muted small">
                    <i class="ti ti-info-circle me-1"></i>
                    <?php echo e(__('This confirmation is required for security purposes.')); ?>

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <?php echo e(__('Cancel')); ?>

                </button>
                <button type="button" class="btn btn-danger" id="forceDeleteConfirmBtn" disabled>
                    <i class="ti ti-trash me-1"></i>
                    <?php echo e(__('Force Delete')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<!-- Standard Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="ti ti-alert-triangle text-danger me-2"></i>
                    <?php echo e(__('Delete Confirmation')); ?>

                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger d-flex align-items-center mb-3">
                    <i class="ti ti-alert-circle me-2"></i>
                    <div>
                        <strong><?php echo e(__('Warning!')); ?></strong> <?php echo e(__('This action cannot be undone.')); ?>

                    </div>
                </div>
                <p id="deleteConfirmText" class="mb-3">
                    <?php echo e(__('Are you sure you want to delete this company?')); ?>

                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <?php echo e(__('Cancel')); ?>

                </button>
                <button type="button" class="btn btn-danger" id="deleteConfirmBtn">
                    <i class="ti ti-trash me-1"></i>
                    <?php echo e(__('Delete')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('style-page'); ?>
<style>
    /* Custom Pagination Styles */
    .pagination-wrapper .pagination {
        margin-bottom: 0;
    }

    .pagination-wrapper .page-link {
        color: #6c757d;
        background-color: #fff;
        border: 1px solid #dee2e6;
        padding: 0.5rem 0.75rem;
        margin: 0 2px;
        border-radius: 0.375rem;
        transition: all 0.15s ease-in-out;
        font-size: 0.875rem;
        line-height: 1.25;
        min-width: 40px;
        text-align: center;
    }

    .pagination-wrapper .page-link:hover {
        color: #0056b3;
        background-color: #e9ecef;
        border-color: #adb5bd;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .pagination-wrapper .page-item.active .page-link {
        color: #fff;
        background-color: #0d6efd;
        border-color: #0d6efd;
        box-shadow: 0 2px 4px rgba(13,110,253,0.25);
    }

    .pagination-wrapper .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #dee2e6;
        opacity: 0.65;
    }

    .pagination-info {
        font-size: 0.875rem;
        color: #6c757d;
    }

    /* Per-page selector styles */
    .per-page-selector .form-select {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.25rem 1.5rem 0.25rem 0.5rem;
        font-size: 0.875rem;
        min-width: 70px;
    }

    .per-page-selector .form-select:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .pagination-wrapper .pagination {
            justify-content: center;
        }

        .pagination-info {
            text-align: center;
            margin-bottom: 1rem;
        }

        .d-flex.justify-content-between {
            flex-direction: column;
            align-items: center !important;
        }

        .pagination-wrapper .page-link {
            padding: 0.375rem 0.5rem;
            font-size: 0.8rem;
            min-width: 35px;
        }

        .d-flex.justify-content-between.align-items-center.mb-3 {
            flex-direction: column;
            gap: 1rem;
        }

        .per-page-selector,
        .total-count {
            text-align: center;
        }
    }

    @media (max-width: 576px) {
        .pagination-wrapper .page-link {
            padding: 0.25rem 0.4rem;
            font-size: 0.75rem;
            min-width: 30px;
            margin: 0 1px;
        }

        .pagination-info {
            font-size: 0.8rem;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script-page'); ?>
    <script>
        $(document).on('change', '#password_switch', function() {
            if ($(this).is(':checked')) {
                $('.ps_div').removeClass('d-none');
                $('#password').attr("required", true);

            } else {
                $('.ps_div').addClass('d-none');
                $('#password').val(null);
                $('#password').removeAttr("required");
            }
        });
        $(document).on('click', '.login_enable', function() {
            setTimeout(function() {
                $('.modal-body').append($('<input>', {
                    type: 'hidden',
                    val: 'true',
                    name: 'login_enable'
                }));
            }, 2000);
        });

        
        // Force delete modal functionality (inline fallback)
        $(document).on('click', '.bs-pass-para-force', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('Force delete clicked (jQuery)');
            
            var el = $(this);
            var form = el.closest('form');
            var confirmText = el.data('confirm-text') || 'This will force delete the company even if modules have dependency errors. Are you sure?';
            
            // Set the confirmation text in the modal
            $('#forceDeleteConfirmText').text(confirmText);
            
            // Clear any previous input and error states
            var input = $('#forceDeleteInput');
            var errorMsg = $('#forceDeleteError');
            var confirmBtn = $('#forceDeleteConfirmBtn');
            
            input.val('');
            input.removeClass('is-invalid is-valid');
            errorMsg.hide();
            confirmBtn.prop('disabled', true);
            
            // Store the form reference for later submission
            $('#forceDeleteModal').attr('data-form-id', form.attr('id'));
            
            // Show the modal
            $('#forceDeleteModal').modal('show');
            
            // Focus on input when modal is shown
            $('#forceDeleteModal').on('shown.bs.modal', function() {
                input.focus();
            });
        });
        
        // Handle input validation
        $(document).on('input', '#forceDeleteInput', function() {
            var input = $(this);
            var confirmBtn = $('#forceDeleteConfirmBtn');
            var errorMsg = $('#forceDeleteError');
            
            if (input.val() === 'DELETE') {
                input.removeClass('is-invalid').addClass('is-valid');
                errorMsg.hide();
                confirmBtn.prop('disabled', false);
            } else {
                input.removeClass('is-valid');
                if (input.val().length > 0) {
                    input.addClass('is-invalid');
                    errorMsg.show();
                } else {
                    input.removeClass('is-invalid');
                    errorMsg.hide();
                }
                confirmBtn.prop('disabled', true);
            }
        });
        
        // Handle confirmation button click
        $(document).on('click', '#forceDeleteConfirmBtn', function() {
            var input = $('#forceDeleteInput');
            if (input.val() === 'DELETE') {
                var modal = $('#forceDeleteModal');
                var formId = modal.attr('data-form-id');
                var form = $('#' + formId);
                
                console.log('Submitting form:', formId, form);
                
                modal.modal('hide');
                if (form.length) {
                    form.submit();
                } else {
                    console.error('Form not found:', formId);
                }
            }
        });

        // Standard delete modal functionality
        $(document).on('click', '.bs-pass-para-delete', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var el = $(this);
            var form = el.closest('form');
            var confirmText = el.data('confirm-text') || 'Are you sure you want to delete this company?';
            // Set the confirmation text in the modal
            $('#deleteConfirmText').text(confirmText);
            // Store the form reference for later submission
            $('#deleteModal').attr('data-form-id', form.attr('id'));
            // Show the modal
            $('#deleteModal').modal('show');
        });
        // Handle confirmation button click for standard delete
        $(document).on('click', '#deleteConfirmBtn', function() {
            var modal = $('#deleteModal');
            var formId = modal.attr('data-form-id');
            var form = $('#' + formId);
            modal.modal('hide');
            if (form.length) {
                form.submit();
            } else {
                console.error('Form not found:', formId);
            }
        });

        // Add loading state for per-page selector
        $(document).on('change', '#per_page', function() {
            var $select = $(this);
            var $form = $select.closest('form');

            // Add loading state
            $select.prop('disabled', true);
            $select.after('<span class="spinner-border spinner-border-sm ms-2" role="status" aria-hidden="true"></span>');

            // Submit form
            $form.submit();
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/system_admin/companies/index.blade.php ENDPATH**/ ?>