<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Plan;
use App\Models\PricingPlan;
use App\Models\SystemAdminModule;
use App\Models\SuperAdminModulePermission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;

class SystemAdminController extends Controller
{
    /**
     * Display the system admin dashboard
     */
    public function dashboard()
    {
        if (!in_array(auth()->user()->type, ['system admin', 'staff'])) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $company = auth()->user()->systemAdminCompany;

        if ($company) {
            // Get stats for company staff
            $totalStaff = User::where('system_admin_company_id', $company->id)
                             ->whereIn('type', ['system admin', 'super admin'])
                             ->count();
            $totalSuperAdmins = User::where('system_admin_company_id', $company->id)
                                   ->where('type', 'super admin')
                                   ->count();
            $totalSystemAdmins = User::where('system_admin_company_id', $company->id)
                                    ->where('type', 'system admin')
                                    ->count();

            $recentStaff = User::where('system_admin_company_id', $company->id)
                              ->whereIn('type', ['system admin', 'super admin'])
                              ->orderBy('created_at', 'desc')
                              ->limit(5)
                              ->get();
        } else {
            // Fallback to old logic for system admins without companies
            $totalStaff = User::where('type', 'super admin')->where('created_by', auth()->id())->count();
            $totalSuperAdmins = User::where('type', 'super admin')->where('created_by', auth()->id())->count();
            $totalSystemAdmins = 1; // Just the current user

            $recentStaff = User::where('type', 'super admin')
                              ->where('created_by', auth()->id())
                              ->orderBy('created_at', 'desc')
                              ->limit(5)
                              ->get();
        }

        $totalCompanies = User::where('type', 'company')->where('created_by', auth()->id())->count();
        $totalModules = SystemAdminModule::count();
        $activeModules = SystemAdminModule::where('is_active', true)->count();

        return view('system_admin.dashboard', compact(
            'totalStaff',
            'totalSuperAdmins',
            'totalSystemAdmins',
            'totalCompanies',
            'totalModules',
            'activeModules',
            'recentStaff',
            'company'
        ));
    }

    /**
     * Display super admins list
     */
    public function superAdmins()
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $superAdmins = User::where('type', 'super admin')
            ->where('created_by', auth()->id())
            ->with(['roles'])
            ->paginate(10);

        return view('system_admin.super_admins.index', compact('superAdmins'));
    }

    /**
     * Show form to create new super admin
     */
    public function createSuperAdmin()
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $modules = SystemAdminModule::active()->ordered()->get();
        
        return view('system_admin.super_admins.create', compact('modules'));
    }

    /**
     * Store new super admin
     */
    public function storeSuperAdmin(Request $request)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:6',
            'modules' => 'array',
            'modules.*' => 'exists:system_admin_modules,id'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Create super admin user
        $superAdmin = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'type' => 'super admin',
            'lang' => 'en',
            'avatar' => '',
            'created_by' => auth()->id(),
            'email_verified_at' => now(),
        ]);

        // Assign super admin role
        $superAdminRole = Role::where('name', 'super admin')->first();
        if ($superAdminRole) {
            $superAdmin->assignRole($superAdminRole);
        }

        // Assign module permissions
        if ($request->has('modules')) {
            foreach ($request->modules as $moduleId) {
                SuperAdminModulePermission::create([
                    'super_admin_id' => $superAdmin->id,
                    'module_id' => $moduleId,
                    'has_access' => true,
                    'permissions' => [], // Default permissions can be set here
                    'granted_by' => auth()->id()
                ]);
            }
        }

        // Sync user to Automatish
        $this->syncUserToAutomatish($superAdmin, $request->password);

        return redirect()->route('system-admin.super-admins')
            ->with('success', __('Super Admin created successfully.'));
    }

    /**
     * Show super admin details
     */
    public function showSuperAdmin($id)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $superAdmin = User::where('type', 'super admin')
            ->where('id', $id)
            ->where('created_by', auth()->id())
            ->with(['roles'])
            ->firstOrFail();

        $modulePermissions = SuperAdminModulePermission::where('super_admin_id', $id)
            ->with(['module', 'grantedBy'])
            ->get();

        return view('system_admin.super_admins.show', compact('superAdmin', 'modulePermissions'));
    }

    /**
     * Show form to edit super admin
     */
    public function editSuperAdmin($id)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $superAdmin = User::where('type', 'super admin')
            ->where('id', $id)
            ->where('created_by', auth()->id())
            ->firstOrFail();

        $modules = SystemAdminModule::active()->ordered()->get();
        $assignedModules = SuperAdminModulePermission::where('super_admin_id', $id)
            ->pluck('module_id')
            ->toArray();

        return view('system_admin.super_admins.edit', compact('superAdmin', 'modules', 'assignedModules'));
    }

    /**
     * Update super admin
     */
    public function updateSuperAdmin(Request $request, $id)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $superAdmin = User::where('type', 'super admin')
            ->where('id', $id)
            ->where('created_by', auth()->id())
            ->firstOrFail();

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $id,
            'password' => 'nullable|string|min:6',
            'modules' => 'array',
            'modules.*' => 'exists:system_admin_modules,id'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update super admin details
        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $superAdmin->update($updateData);

        // Update module permissions
        SuperAdminModulePermission::where('super_admin_id', $id)->delete();

        if ($request->has('modules')) {
            foreach ($request->modules as $moduleId) {
                SuperAdminModulePermission::create([
                    'super_admin_id' => $id,
                    'module_id' => $moduleId,
                    'has_access' => true,
                    'permissions' => [],
                    'granted_by' => auth()->id()
                ]);
            }
        }

        return redirect()->route('system-admin.super-admins')
            ->with('success', __('Super Admin updated successfully.'));
    }

    /**
     * Delete super admin
     */
    public function deleteSuperAdmin($id)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $superAdmin = User::where('type', 'super admin')
            ->where('id', $id)
            ->where('created_by', auth()->id())
            ->firstOrFail();

        // Delete module permissions
        SuperAdminModulePermission::where('super_admin_id', $id)->delete();

        // Delete super admin
        $superAdmin->delete();

        return redirect()->route('system-admin.super-admins')
            ->with('success', __('Super Admin deleted successfully.'));
    }

    /**
     * Manage modules
     */
    public function modules()
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $modules = SystemAdminModule::ordered()->get();

        return view('system_admin.modules.index', compact('modules'));
    }

    /**
     * Show module permissions for a specific super admin
     */
    public function modulePermissions($superAdminId)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $superAdmin = User::where('type', 'super admin')
            ->where('id', $superAdminId)
            ->where('created_by', auth()->id())
            ->firstOrFail();

        $modules = SystemAdminModule::active()->ordered()->get();
        $permissions = SuperAdminModulePermission::where('super_admin_id', $superAdminId)
            ->with('module')
            ->get()
            ->keyBy('module_id');

        return view('system_admin.permissions.index', compact('superAdmin', 'modules', 'permissions'));
    }

    /**
     * Update module permissions for super admin
     */
    public function updateModulePermissions(Request $request, $superAdminId)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $superAdmin = User::where('type', 'super admin')
            ->where('id', $superAdminId)
            ->where('created_by', auth()->id())
            ->firstOrFail();

        $validator = Validator::make($request->all(), [
            'permissions' => 'array',
            'permissions.*' => 'array',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update permissions for each module
        foreach ($request->permissions ?? [] as $moduleId => $modulePermissions) {
            $permission = SuperAdminModulePermission::updateOrCreate(
                [
                    'super_admin_id' => $superAdminId,
                    'module_id' => $moduleId
                ],
                [
                    'has_access' => isset($modulePermissions['has_access']),
                    'permissions' => $modulePermissions['specific'] ?? [],
                    'granted_by' => auth()->id()
                ]
            );
        }

        return redirect()->back()
            ->with('success', __('Module permissions updated successfully.'));
    }

    /**
     * Display companies list
     */
    public function companies(Request $request)
    {
        // Check permissions for different user types
        $hasAccess = false;
        if (auth()->user()->type == 'system admin') {
            $hasAccess = true;
        } elseif (auth()->user()->type == 'staff') {
            $hasAccess = auth()->user()->can('view sub accounts');
        }

        if (!$hasAccess) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // Get per page value from request, default to 10, max 100
        $perPage = $request->get('per_page', 10);
        $perPage = in_array($perPage, [10, 25, 50, 100]) ? $perPage : 10;

        $companies = User::where('type', 'company')
            ->where('created_by', auth()->id())
            ->with(['plan'])
            ->paginate($perPage);

        $totalCompaniesCount = User::where('type', 'company')
            ->where('created_by', auth()->id())
            ->count();

        return view('system_admin.companies.index', compact('companies', 'totalCompaniesCount'));
    }

    /**
     * Show form to create new company
     */
    public function createCompany()
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $plans = PricingPlan::active()->where('plan_type', 'subaccount')->get();
        
        return view('system_admin.companies.create', compact('plans'));
    }

    /**
     * Store new company
     */
    public function storeCompany(Request $request)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:6',
            'plan_id' => 'nullable|exists:pricing_plans,id'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Get pricing plan and its module permissions
        $pricingPlan = null;
        $modulePermissions = [];
        
        if ($request->plan_id) {
            $pricingPlan = PricingPlan::find($request->plan_id);
            if ($pricingPlan && $pricingPlan->module_permissions) {
                $modulePermissions = $pricingPlan->module_permissions;
            }
        }

        // Create company user
        $company = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'type' => 'company',
            'lang' => 'en',
            'avatar' => '',
            'plan' => $request->plan_id ?? 1,
            'plan_expire_date' => null,
            'module_permissions' => $modulePermissions,
            'created_by' => auth()->id(),
            'email_verified_at' => now(),
        ]);

        // Assign company role
        $companyRole = Role::where('name', 'company')->first();
        if ($companyRole) {
            $company->assignRole($companyRole);
        }

        // Assign plan with limits and permissions
        if ($pricingPlan) {
            $company->assignPlan($pricingPlan->id);
            // Ensure pricing plan permissions are assigned to Spatie permission system
            $company->assignPricingPlanPermissions();
        }

        // Sync module permissions to external modules
        $this->syncModulePermissions($company, $modulePermissions);

        // Sync user to Automatish
        $this->syncUserToAutomatish($company, $request->password);

        return redirect()->route('system-admin.companies')
            ->with('success', __('Company created successfully.'));
    }

    /**
     * Show company details
     */
    public function showCompany($id)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $company = User::where('type', 'company')
            ->where('id', $id)
            ->where('created_by', auth()->id())
            ->with(['plan'])
            ->firstOrFail();

        return view('system_admin.companies.show', compact('company'));
    }

    /**
     * Show form to edit company
     */
    public function editCompany($id)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $company = User::where('type', 'company')
            ->where('id', $id)
            ->where('created_by', auth()->id())
            ->firstOrFail();

        $plans = PricingPlan::active()->where('plan_type', 'subaccount')->get();

        return view('system_admin.companies.edit', compact('company', 'plans'));
    }

    /**
     * Update company
     */
    public function updateCompany(Request $request, $id)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $company = User::where('type', 'company')
            ->where('id', $id)
            ->where('created_by', auth()->id())
            ->firstOrFail();

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $id,
            'password' => 'nullable|string|min:6',
            'plan_id' => 'nullable|exists:pricing_plans,id'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Get pricing plan and its module permissions
        $pricingPlan = null;
        $modulePermissions = [];
        $planChanged = false;
        
        if ($request->plan_id && $request->plan_id != $company->plan) {
            $pricingPlan = PricingPlan::find($request->plan_id);
            if ($pricingPlan && $pricingPlan->module_permissions) {
                $modulePermissions = $pricingPlan->module_permissions;
                $planChanged = true;
            }
        }

        // Update company details
        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'plan' => $request->plan_id ?? $company->plan,
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        // Update module permissions if plan changed
        if ($planChanged) {
            $updateData['module_permissions'] = $modulePermissions;
        }

        $company->update($updateData);

        // Assign new plan with limits and permissions if plan changed
        if ($planChanged && $pricingPlan) {
            $company->assignPlan($pricingPlan->id);
            
            // Sync module permissions to external modules
            $this->syncModulePermissions($company, $modulePermissions);
        }

        return redirect()->route('system-admin.companies')
            ->with('success', __('Company updated successfully.'));
    }

    /**
     * Delete company
     */
    public function deleteCompany($id)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $company = User::where('type', 'company')
            ->where('id', $id)
            ->where('created_by', auth()->id())
            ->firstOrFail();

        // Delete company
        $company->delete();

        return redirect()->route('system-admin.companies')
            ->with('success', __('Company deleted successfully.'));
    }

    /**
     * Force delete company
     */
    public function forceDeleteCompany($id)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $company = User::where('type', 'company')
            ->where('id', $id)
            ->where('created_by', auth()->id())
            ->firstOrFail();

        // Force delete company
        $company->forceDelete();

        return redirect()->route('system-admin.companies')
            ->with('success', __('Company force deleted successfully.'));
    }

    /**
     * Login as company
     */
    public function loginCompany($id)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $id = \Crypt::decrypt($id);
        $company = User::where('type', 'company')
            ->where('id', $id)
            ->where('created_by', auth()->id())
            ->firstOrFail();

        if ($company->is_enable_login == 1) {
            $company->is_enable_login = 0;
            $company->save();
            return redirect()->route('system-admin.companies')->with('success', __('Company login disabled successfully.'));
        } else {
            $company->is_enable_login = 1;
            $company->save();
            return redirect()->route('system-admin.companies')->with('success', __('Company login enabled successfully.'));
        }
    }

    /**
     * Reset company password
     */
    public function resetCompanyPassword($id)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $id = \Crypt::decrypt($id);
        $company = User::where('type', 'company')
            ->where('id', $id)
            ->where('created_by', auth()->id())
            ->firstOrFail();

        return view('system_admin.companies.reset_password', compact('company'));
    }

    /**
     * Update company password
     */
    public function updateCompanyPassword(Request $request, $id)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $company = User::where('type', 'company')
            ->where('id', $id)
            ->where('created_by', auth()->id())
            ->firstOrFail();

        $validator = Validator::make($request->all(), [
            'password' => 'required|string|min:6|confirmed',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $company->update([
            'password' => Hash::make($request->password),
            'is_enable_login' => 1,
        ]);

        return redirect()->route('system-admin.companies')
            ->with('success', __('Company password updated successfully.'));
    }

    /**
     * Sync module permissions to external modules
     */
    private function syncModulePermissions($company, $modulePermissions)
    {
        if (empty($modulePermissions)) {
            return;
        }

        // Check if OMX Flow permissions are included in the pricing plan
        if (isset($modulePermissions['omx_flow'])) {
            $this->syncToOmxFlow($company, $modulePermissions['omx_flow']);
        }

        // Sync permissions to external modules if they exist
        try {
            // Check if external module sync methods exist and call them
            if (method_exists($company, 'syncToExternalModules')) {
                $company->syncToExternalModules($modulePermissions);
            }

            // Log the sync operation
            \Log::info('Module permissions synced for company: ' . $company->id, [
                'company_id' => $company->id,
                'module_permissions' => $modulePermissions
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to sync module permissions for company: ' . $company->id, [
                'error' => $e->getMessage(),
                'company_id' => $company->id
            ]);
        }
    }

    /**
     * Sync user to OMX Flow module
     */
    private function syncToOmxFlow($user, $omxFlowPermissions)
    {
        try {
            // Get enabled OMX Flow modules
            $modules = \App\Models\ModuleIntegration::enabled()->get();

            foreach ($modules as $module) {
                // Only sync to modules that match the OMX Flow module
                if ($module->name === 'omx_flow' || $module->name === 'omx-flow' || $module->name === 'OMX FLOW') {
                    $moduleIntegrationController = app(\App\Http\Controllers\ModuleIntegrationController::class);

                    // Prepare additional fields for company creation with default super admin email
                    $additionalFields = [
                        'company_name' => $user->name,
                        'company_description' => 'Company created via pricing plan integration',
                        'super_admin_email' => '<EMAIL>'
                    ];

                    // Send to external module with permissions and additional data
                    $result = $moduleIntegrationController->syncUserToModule($user, $module, $omxFlowPermissions, $additionalFields);

                    \Log::info('OMX Flow sync result for company', [
                        'user_id' => $user->id,
                        'user_email' => $user->email,
                        'module_name' => $module->name,
                        'permissions' => $omxFlowPermissions,
                        'additional_fields' => $additionalFields,
                        'sync_successful' => $result
                    ]);
                }
            }
        } catch (\Exception $e) {
            \Log::error('Failed to sync company to OMX Flow', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'permissions' => $omxFlowPermissions,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Sync user to Automatish
     */
    private function syncUserToAutomatish($user, $plainPassword = null)
    {
        try {
            \Log::info('SystemAdminController: Attempting to sync user to Automatish', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_type' => $user->type,
                'plain_password_provided' => $plainPassword ? 'Yes' : 'No'
            ]);

            $moduleIntegrationController = app(\App\Http\Controllers\ModuleIntegrationController::class);
            $result = $moduleIntegrationController->syncUserToAutomatish($user, $plainPassword);

            \Log::info('SystemAdminController: Automatish sync result', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'sync_successful' => $result
            ]);

            return $result;
        } catch (\Exception $e) {
            \Log::error('SystemAdminController: Failed to sync user to Automatish', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
}
